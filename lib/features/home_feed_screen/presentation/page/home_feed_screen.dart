import 'dart:async';
import 'dart:io';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/authentication/model/get_brands_model.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/live_stream/model/live_user_model.dart';
import 'package:flowkar/features/live_stream/presentation/live_stream.dart';
import 'package:flowkar/features/notification/page/notification_screen.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_bloc.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_event.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_state.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/story_screen.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/widgets/custom/all_caught_up_massage.dart';
import 'package:flowkar/features/widgets/custom/flowkar_get_pogress_bar/flowkar_pogress_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:shadow_widget/shadow_widget.dart';
import 'package:story_editor/story_editor.dart';

class HomeFeedScreen extends StatefulWidget {
  const HomeFeedScreen({super.key});

  static Widget builder(BuildContext context) {
    return HomeFeedScreen();
  }

  @override
  State<HomeFeedScreen> createState() => _HomeFeedScreenState();
}

class _HomeFeedScreenState extends State<HomeFeedScreen> with SingleTickerProviderStateMixin {
  late ConnectivityState? connectivityState;
  late TabController _tabController;
  late ScrollController _scrollController;
  double _lastOffset = 0;
  bool _showAppBar = true;
  bool _isInitialLoadDone = false;
  ScrollController? storyController;
  bool showHorizontalList = true;
  int selectedIndex = 0;
  late int brandId;
  String? userType;
  bool _isLoadingMore = false;

  bool _showAllCaughtUp = false;
  Timer? _allCaughtUpTimer;
  bool _hasShownAllCaughtUp = false;
  bool _isAtBottom = false;
  bool _wasAtBottomBefore = false;
  Timer? _scrollEndTimer;

  bool _isRefreshing = false;

  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;

  // Add cache for posts and videos
  final Map<int, dynamic> _postCache = {};
  final Map<int, dynamic> _videoCache = {};
  late bool isPostPermission;

  // Add story cache
  // final Map<String, ImageProvider> _storyImageCache = {};

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  @override
  void initState() {
    connectivityState = context.read<ConnectivityBloc>().state;
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
    context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
    super.initState();
    initializeNotifications();
    context
        .read<AuthBloc>()
        .add(OneSignalIdApiEvent(onesignalId: Prefobj.preferences?.get(Prefkeys.SUBSCRIPTIONID) ?? ''));

    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController()..addListener(_scrollListener);
    storyController = ScrollController()..addListener(_scrollStoryListener);
    // context.read<UserManagementBloc>().add(GetBrandsAPI());

    scrollTopNotifier.addListener(() {
      if (scrollTopNotifier.value) {
        _handleHomeDoubleClick();
      }
    });
  }

  void _handleHomeDoubleClick() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 1500),
        curve: Curves.easeInOut,
      );
    }
    scrollTopNotifier.value = false;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = context.read<HomeFeedBloc>().state;
    if (!_isInitialLoadDone) {
      if (!_isInitialLoadDone && (state.posts.isEmpty)) {
        context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: 1));
        // context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: 1));
        context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
        context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
        _isInitialLoadDone = true;
      }
    }
  }

  @override
  void dispose() {
    _offlineMessageTimer?.cancel();
    _tabController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _allCaughtUpTimer?.cancel();
    if (storyController != null) {
      storyController!.removeListener(_scrollStoryListener);
      storyController!.dispose();
    }
    super.dispose();
  }

  void _scrollListener() {
    if (!mounted) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    final threshold = 100.0; // Adjust this value as needed

    // Check if we're at the bottom
    final isAtBottom = currentScroll >= maxScroll - threshold;

    // Cancel any previous timer
    _scrollEndTimer?.cancel();

    if (isAtBottom) {
      // Set a timer to confirm we've stopped scrolling
      _scrollEndTimer = Timer(const Duration(milliseconds: 300), () {
        if (mounted && _scrollController.position.pixels >= maxScroll - threshold) {
          setState(() {
            _isAtBottom = true;
          });
          _handlePagination();
        }
      });
    } else {
      setState(() {
        _isAtBottom = false;
        _showAllCaughtUp = false;
      });
    }

    // Handle horizontal list visibility
    if (_lastOffset != currentScroll) {
      _handleHorizontalListVisibility();
      _lastOffset = currentScroll;
    }
  }

  void _scrollStoryListener() {
    if (storyController?.position.pixels == storyController?.position.maxScrollExtent) {
      final state = context.read<HomeFeedBloc>().state;
      if (!state.isLoadingMore) {
        // context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
      }
    }
  }

  void _handleHorizontalListVisibility() {
    if (!mounted) return;

    double currentOffset = _scrollController.offset;
    bool isScrollingUp = currentOffset < _lastOffset;
    double itemHeight = 116.h;
    int currentIndex = (currentOffset / itemHeight).floor();

    if (mounted) {
      setState(() {
        if (currentIndex == 0 || (currentIndex == 1 && currentOffset > 0)) {
          showHorizontalList = true;
        } else if (currentIndex >= 1) {
          showHorizontalList = false;
        }
        if (currentOffset <= 0 || isScrollingUp) {
          _showAppBar = true;
        } else {
          _showAppBar = false;
        }
      });
    }
  }

  void _handlePagination() {
    if (!mounted || _isLoadingMore) return;

    final state = context.read<HomeFeedBloc>().state;
    connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState!.isConnected) return;

    // Check if there's more data to load
    final nextPage = state.postResponsemodel?.nextPage;
    final isLoading = state.isLoadingMore;
    final hasMoreData = nextPage != null;
    final isPaginationComplete = !hasMoreData && !isLoading;
    final isShimmerLoading = state.homeFeedLoading;

    if (_isAtBottom && hasMoreData && !isLoading) {
      setState(() {
        _isLoadingMore = true;
      });
      context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: state.postPage + 1));
    } else if (_isAtBottom && isPaginationComplete && !isShimmerLoading) {
      if (!_wasAtBottomBefore || !_hasShownAllCaughtUp) {
        setState(() {
          _showAllCaughtUp = true;
          _hasShownAllCaughtUp = true;
        });

        _allCaughtUpTimer?.cancel();
        _allCaughtUpTimer = Timer(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _showAllCaughtUp = false;
            });
          }
        });
      }
    }

    _wasAtBottomBefore = _isAtBottom;
  }

  Future<void> _refreshFeed() async {
    connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState!.isConnected) {
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 3), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });

          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });

      return;
    } else {
      final state = context.read<HomeFeedBloc>().state;

      setState(() {
        _isRefreshing = true;
        _showAppBar = true;
        showHorizontalList = true;
        _isLoadingMore = false;
        _showAllCaughtUp = false;
        _hasShownAllCaughtUp = false;
        _isAtBottom = false;
        _wasAtBottomBefore = false;
      });

      _isRefreshAttemptedOffline = false;
      _showOfflineMessage = false;
      _allCaughtUpTimer?.cancel();
      _scrollEndTimer?.cancel();

      if (state.newStoryData.isNotEmpty) state.newStoryData.clear();
      if (state.posts.isNotEmpty) state.posts.clear();
      if (state.livenewStoryData.isNotEmpty) state.livenewStoryData.clear();

      _postCache.clear();
      _videoCache.clear();

      context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: 1));
      context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
      context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
      context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));

      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void initializeNotifications() {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<bool> shouldShowBetaPost() async {
    final dismissedAt = Prefobj.preferences?.get(Prefkeys.BETA_POST_DISMISSED_AT);

    if (dismissedAt == null) return true;

    final now = DateTime.now().millisecondsSinceEpoch;
    //24 * 60 * 60 * 1000 ---- > 24 HOURS
    final fiveMinutesInMillis = 24 * 60 * 60 * 1000;
    // 1 * 60 * 1000; ----> 2 MIN

    return now - dismissedAt > fiveMinutesInMillis;
  }

  @override
  Widget build(BuildContext context) {
    connectivityState = context.read<ConnectivityBloc>().state;
    userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    isPostPermission = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;

    SocketService.response(APIConfig.notification, (response) {
      if (isMessageScreen.value == true) {
        if (massageUserID.value.toString() == response['from'].toString()) {
          return null;
        } else {
          if (mounted) {
            showLocalNotification(response);
          }
        }
      } else {
        if (mounted) {
          showLocalNotification(response);
        }
      }
    });

    return SafeArea(
      child: Stack(
        alignment: Alignment.center,
        children: [
          Scaffold(
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(50.h),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 400),
                curve: _showAppBar ? Curves.easeIn : Curves.easeInOut,
                height: _showAppBar ? 50.h : 0,
                // clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(color: Theme.of(context).customColors.white),
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 400),
                  opacity: _showAppBar ? 1 : 0,
                  child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
                    builder: (context, state) {
                      return _buildAppBar(context);
                    },
                  ),
                ),
              ),
            ),
            body: BlocBuilder<SurveyBloc, SurveyState>(
              builder: (context, surveyState) {
                return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                  builder: (context, state) {
                    return Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // _showAppBar ? buildSizedBoxH(0) : buildSizedBoxH(20),
                            FlowkarProgressIndicator(progressNotifier: progressNotifier)
                          ],
                        ),
                        BlocListener<ConnectivityBloc, ConnectivityState>(
                          listener: (context, connectivityState) {
                            if (connectivityState.isReconnected) {
                              if (state.posts.isEmpty) {
                                _refreshFeed();
                              }
                            }
                          },
                          child: Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(bottom: 0.h),
                              child: _buildFeedTab(state, surveyState),
                              // TabBarView(
                              //   controller: _tabController,
                              //   children: [
                              //     _buildFeedTab(state, surveyState),
                              //     _buildVideoTab(state, surveyState),
                              //   ],
                              // ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
          _buildOfflineMessage(_showOfflineMessage)
        ],
      ),
    );
  }

// MARK: App Bar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      alignment: Alignment.center,
      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 24.sp),
      leading: [
        InkWell(
          onTap: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: NotificationPage(),
              withNavBar: false,
              pageTransitionAnimation: PageTransitionAnimation.cupertino,
            );
          },
          child: ShadowWidget(
            color: Colors.black12,
            blurRadius: 5,
            child: Container(
              height: 36.h,
              width: 36.w,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.white,
                shape: BoxShape.circle,
              ),
              child: CustomImageView(imagePath: Assets.images.svg.other.svgNotificationUnfill.path),
            ),
          ),
        ),
        Spacer(),
        BlocBuilder<LeaderboardBloc, LeaderboardState>(
          builder: (context, leaderboardState) {
            return InkWell(
              onTap: leaderboardState.isRewardDataLoading
                  ? () {}
                  : () {
                      context.read<LeaderboardBloc>().add(GetRewardDataEvent(context: context));
                    },
              child: ShadowWidget(
                color: Colors.black12,
                blurRadius: 5,
                child: Container(
                  height: 36.h,
                  width: 36.w,
                  padding: EdgeInsets.symmetric(horizontal: 9.w, vertical: 9.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.white,
                    shape: BoxShape.circle,
                    // border: Border.all(
                    //     color: Colors.black12.withOpacity(0.05),
                    //     width: 2.2,
                    //     strokeAlign: BorderSide.strokeAlignOutside)
                    // boxShadow: [BoxShadow(color: _showAppBar ? Colors.red : Colors.white, blurRadius: 5)],
                  ),
                  child: CustomImageView(imagePath: Assets.images.svg.homeFeed.svgRewardTask.path),
                ),
              ),
            );
          },
        ),
        buildSizedBoxW(16),
        InkWell(
          onTap: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: UserProfileScreen(),
              withNavBar: false,
              pageTransitionAnimation: PageTransitionAnimation.cupertino,
            );
          },
          child: ValueListenableBuilder<String>(
              valueListenable: profileImageNotifier,
              builder: (context, imagePath, child) {
                userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
                return ShadowWidget(
                  color: Colors.black12,
                  blurRadius: 5,
                  child: Container(
                    height: 36.0.h,
                    width: 36.0.w,
                    padding: (imagePath == AssetConstants.pngUser) ? EdgeInsets.all(8) : EdgeInsets.zero,
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.white,
                      shape: BoxShape.circle,
                      // border: Border.all(
                      //     color: Colors.black12.withOpacity(0.05),
                      //     width: 2.2,
                      //     strokeAlign: BorderSide.strokeAlignOutside)
                      // boxShadow: [BoxShadow(color: _showAppBar ? Colors.black12 : Colors.white, blurRadius: 5)],
                    ),
                    child: CustomImageView(
                      fit: (imagePath == AssetConstants.pngUser) ? BoxFit.contain : BoxFit.cover,
                      imagePath: imagePath,
                      alignment: ((imagePath == AssetConstants.pngUser) && imagePath.isEmpty) ? Alignment.center : null,
                    ),
                  ),
                );
              }),
        ),
      ],
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: Platform.isAndroid ? 80.h : 60.h, // Better positioning
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

// MARK: Brand Tile
  Widget buildBrandTile(BuildContext context, BrandData brand, int index) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ListTile(
        minTileHeight: 60.h,
        leading: Stack(
          children: [
            CustomImageView(
              fit: BoxFit.cover,
              radius: BorderRadius.circular(100.r),
              imagePath: brand.logo.isEmpty
                  ? Assets.images.pngs.other.pngPlaceholder.path
                  : "${APIConfig.mainbaseURL}${brand.logo}",
              height: 42.h,
              width: 42.w,
            ),
            // if (selectedIndex == index)
            if (brand.id == brandId)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_circle_rounded,
                    color: Theme.of(context).primaryColor,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          brand.name,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
        ),
        subtitle: brand.domain.isNotEmpty
            ? Text(
                brand.domain,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 12.sp,
                      color: Theme.of(context).customColors.subHeadingcolor,
                    ),
              )
            : null,
        onTap: () {
          setState(() {
            brandId = brand.id;
            // selectedIndex = index;
            // brandNameNotifier.value = brand.name;
            VibrationHelper.singleShortBuzz();
            context.read<AuthBloc>().add(CurentBrandIDAPI(brandId: brand.id, context: context));
            NavigatorService.goBack();
          });
        },
      ),
    );
  }

// MARK: Tab Bar
  // ignore: unused_element
  Widget _buildTabBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 60.w, vertical: 4.h),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xffF0EFEF),
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black,
        dividerColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateColor.transparent,
        indicator: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(25),
        ),
        onTap: (value) {
          context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
          VibrationHelper.singleShortBuzz();
        },
        labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        unselectedLabelStyle:
            Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        tabs: const [
          Tab(text: "Feed"),
          Tab(text: "Video"),
        ],
      ),
    );
  }

  Widget _buildHorizontalList(HomeFeedState state, {required BuildContext ctx}) {
    return Container(
        margin: EdgeInsets.only(bottom: 6.h),
        child: Align(alignment: Alignment.centerLeft, child: _buildStory(state, ctx: ctx)));
  }

// MARK: Story
  Widget _buildStory(HomeFeedState state, {required BuildContext ctx}) {
    final loggedInUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);
    NewStory? loggedInUserStory;

    final uniqueStories = <NewStory>[];
    final userIdsSeen = <int>{};
    for (var story in state.newStoryData) {
      if (story.userId == int.parse(loggedInUserId)) {
        loggedInUserStory ??= story;
      } else if (!userIdsSeen.contains(story.userId)) {
        userIdsSeen.add(story.userId ?? 0);
        uniqueStories.add(story);
      }
    }

    uniqueStories.removeWhere((story) => story.userId == int.parse(loggedInUserId));

    final filteredStories = uniqueStories.where((story) => story.userId != int.parse(loggedInUserId)).toList();
    // final firstUsername = filteredStories.isNotEmpty ? filteredStories.first.username.toString() : "Unknown User";

    return state.storyisloding == true
        ? LoadingAnimationWidget()
        : Padding(
            padding: EdgeInsets.only(left: 12.0.w, top: 6.0.h),
            child: SizedBox(
              height: 120.h,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: AlwaysScrollableScrollPhysics(),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () async {
                        bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                        bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);
                        if (loggedInUserStory != null) {
                          // PersistentNavBarNavigator.pushNewScreen(
                          //   context,
                          //   screen: StoryScreen(
                          //     users: [loggedInUserStory],
                          //     index: 1,
                          //     isHilight: false,
                          //   ),
                          //   withNavBar: false,
                          // );
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: StoryScreen(
                              users: [loggedInUserStory],
                              index: 0,
                              isHilight: false,
                            ),
                            withNavBar: false,
                            customPageRoute: PageRouteBuilder(
                              opaque: false,
                              barrierColor: Colors.transparent,
                              transitionDuration: Duration(milliseconds: 250),
                              reverseTransitionDuration: Duration(milliseconds: 250),
                              pageBuilder: (context, animation, secondaryAnimation) {
                                return StoryScreen(
                                  users: [loggedInUserStory ?? NewStory()],
                                  index: 0,
                                  isHilight: false,
                                );
                              },
                              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                final curvedAnimation = CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeInOut,
                                );

                                return SlideTransition(
                                  position: Tween<Offset>(
                                    begin: Offset(0, 1),
                                    end: Offset.zero,
                                  ).animate(curvedAnimation),
                                  child: SlideTransition(
                                    position: Tween<Offset>(
                                      begin: Offset.zero,
                                      end: Offset(0, 1),
                                    ).animate(CurvedAnimation(
                                      parent: secondaryAnimation,
                                      curve: Curves.easeInOut,
                                    )),
                                    child: child,
                                  ),
                                );
                              },
                            ),
                          );
                        } else {
                          if (isPostPermission == true) {
                            PersistentNavBarNavigator.pushNewScreen(context,
                                screen: flowkarStoryEditor(
                                  centerText: "Start Your Design",
                                  isInstagram: isInstagram,
                                  isfacebook: isFacebook,
                                  onDone: (media, instagram, facebook) {
                                    context.read<HomeFeedBloc>().add(
                                          UploadStoryApiEvent(
                                            uploadFiles: File(media),
                                            music: '',
                                            title: '',
                                            isFacebook: facebook,
                                            isInstagram: instagram,
                                          ),
                                        );
                                    Logger.lOG("onDone $media");
                                    Logger.lOG("insta $instagram");
                                    Logger.lOG("facebook $facebook");
                                    if (Navigator.canPop(context)) {
                                      Navigator.of(context).pop();
                                    }
                                    // Navigator.pop(context);
                                  },
                                  onDoneHighlight: (media, title) {
                                    Logger.lOG("onDoneHighlight $title");
                                    context.read<HomeFeedBloc>().add(
                                          UploadStoryApiEvent(
                                            uploadFiles: File(media),
                                            music: '',
                                            title: title,
                                            isFacebook: false,
                                            isInstagram: false,
                                          ),
                                        );
                                    if (Navigator.canPop(context)) {
                                      Navigator.of(context).pop();
                                    }
                                  },
                                ),
                                withNavBar: false);
                          } else {
                            showToastNoPermission(
                              access: "upload story",
                            );
                          }
                          // _buildAddStoryAlert(context);
                        }
                      },
                      onLongPress: isPostPermission == false
                          ? () {
                              showToastNoPermission(access: "upload story");
                            }
                          : () async {
                              bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                              bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);

                              PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: flowkarStoryEditor(
                                    centerText: "Start Your Design",
                                    isInstagram: isInstagram,
                                    isfacebook: isFacebook,
                                    onDone: (media, instagram, facebook) {
                                      Logger.lOG("onDone $media");

                                      context.read<HomeFeedBloc>().add(
                                            UploadStoryApiEvent(
                                              uploadFiles: File(media),
                                              music: '',
                                              title: '',
                                              isFacebook: facebook,
                                              isInstagram: instagram,
                                            ),
                                          );
                                      if (Navigator.canPop(context)) {
                                        Navigator.of(context).pop();
                                      }
                                      // Navigator.pop(context);
                                    },
                                    onDoneHighlight: (media, title) {
                                      Logger.lOG("onDoneHighlight $title");
                                      context.read<HomeFeedBloc>().add(
                                            UploadStoryApiEvent(
                                              uploadFiles: File(media),
                                              music: '',
                                              title: title,
                                              isFacebook: false,
                                              isInstagram: false,
                                            ),
                                          );
                                      if (Navigator.canPop(context)) {
                                        Navigator.of(context).pop();
                                      }
                                    },
                                  ),
                                  withNavBar: false);
                            },
                      child: _buildStoryItem(
                        'Your Story',
                        loggedInUserStory?.userprofile?.isEmpty ?? true
                            ? Assets.images.pngs.other.pngPlaceholder.path
                            : "${loggedInUserStory?.userprofile}",
                        isSelect: true,
                      ),
                    ),
                    _buildLiveUser(state),
                    if (filteredStories.isNotEmpty)
                      ...filteredStories.map((story) => GestureDetector(
                            onTap: () {
                              // PersistentNavBarNavigator.pushNewScreen(
                              //   context,
                              //   screen: StoryScreen(
                              //     users: filteredStories,
                              //     index: filteredStories.indexOf(story),
                              //     isHilight: false,
                              //   ),
                              //   withNavBar: false,
                              // );

                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: StoryScreen(
                                  users: filteredStories,
                                  index: filteredStories.indexOf(story),
                                  isHilight: false,
                                ),
                                withNavBar: false,
                                customPageRoute: PageRouteBuilder(
                                  opaque: false,
                                  barrierColor: Colors.transparent,
                                  transitionDuration: Duration(milliseconds: 250),
                                  reverseTransitionDuration: Duration(milliseconds: 250),
                                  pageBuilder: (context, animation, secondaryAnimation) {
                                    return StoryScreen(
                                      users: filteredStories,
                                      index: filteredStories.indexOf(story),
                                      isHilight: false,
                                    );
                                  },
                                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                    final curvedAnimation = CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.easeInOut,
                                    );

                                    return SlideTransition(
                                      position: Tween<Offset>(
                                        begin: Offset(0, 1),
                                        end: Offset.zero,
                                      ).animate(curvedAnimation),
                                      child: SlideTransition(
                                        position: Tween<Offset>(
                                          begin: Offset.zero,
                                          end: Offset(0, 1),
                                        ).animate(CurvedAnimation(
                                          parent: secondaryAnimation,
                                          curve: Curves.easeInOut,
                                        )),
                                        child: child,
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                _buildStoryItem(
                                  "${story.username}",
                                  story.userprofile?.isEmpty ?? true
                                      ? AssetConstants.pngUserReomve
                                      : "${story.userprofile}",
                                  isSelect: false,
                                  url: story.stories != null && story.stories!.isNotEmpty
                                      ? story.stories![0].storyfile
                                      : null,
                                )
                              ],
                            ),
                          )),
                  ],
                ),
              ),
            ),
          );
  }

// MARK:Story Item
  Widget _buildStoryItem(String title, String imageUrl, {bool isSelect = false, String? url, void Function()? onTap}) {
    return Container(
      height: 120.h,
      width: 85.w,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: isSelect ? Colors.transparent : Theme.of(context).primaryColor, width: 1.w),
        image: DecorationImage(image: NetworkImage(url.toString()), fit: BoxFit.fill),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          Padding(
            padding: EdgeInsets.only(left: isSelect ? 0 : 0.0.w, top: isSelect ? 30.h : 5.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.bottomCenter,
                  clipBehavior: Clip.none,
                  children: [
                    isSelect
                        ? Align(
                            alignment: Alignment.center,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                border: Border.all(
                                  color: Theme.of(context).textTheme.titleSmall!.color!,
                                  width: 1.w,
                                ),
                              ),
                              child: ClipRRect(
                                clipBehavior: Clip.antiAlias,
                                child: ValueListenableBuilder<String>(
                                  valueListenable: profileImageNotifier,
                                  builder: (ctx, imagePath, child) {
                                    return InkWell(
                                      onTap: onTap,
                                      child: Container(
                                        height: 40.h,
                                        width: 40.w,
                                        padding: imagePath == AssetConstants.pngUser
                                            ? EdgeInsets.symmetric(horizontal: 5, vertical: 8)
                                            : EdgeInsets.zero,
                                        child: CustomImageView(
                                          fit: BoxFit.contain,
                                          radius:
                                              imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(50.r),
                                          imagePath: imagePath,
                                          fallbackImage: AssetConstants.pngUserReomve,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          )
                        : SizedBox(
                            height: 113.h,
                            width: 85.w,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                                  padding: EdgeInsets.all(1.5.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white,
                                    border: Border.all(
                                      color: Theme.of(context).textTheme.titleSmall!.color!,
                                      width: 1.w,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    clipBehavior: Clip.antiAlias,
                                    child: Container(
                                      height: 28.h,
                                      width: 28.w,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade50,
                                        borderRadius: BorderRadius.circular(50.r),
                                      ),
                                      child: CustomImageView(imagePath: imageUrl, fit: BoxFit.cover),
                                    ),
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    // color: Theme.of(context).primaryColor.withOpacity(0.5),
                                    borderRadius: BorderRadius.vertical(bottom: Radius.circular(10.r)),
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
                                  child: Row(
                                    children: [
                                      Flexible(
                                        child: Text(
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          textAlign: TextAlign.start,
                                          title,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(fontSize: 11.sp, color: Theme.of(context).customColors.white),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                    isSelect
                        ? Positioned(
                            bottom: -10,
                            child: InkWell(
                                onTap: isPostPermission == false
                                    ? () {
                                        showToastNoPermission(access: "upload story");
                                      }
                                    : () async {
                                        bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                                        bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);
                                        PersistentNavBarNavigator.pushNewScreen(
                                          context,
                                          screen: flowkarStoryEditor(
                                            centerText: "Start Your Design",
                                            isInstagram: isInstagram,
                                            isfacebook: isFacebook,
                                            onDone: (media, instagram, facebook) {
                                              context.read<HomeFeedBloc>().add(UploadStoryApiEvent(
                                                    uploadFiles: File(media),
                                                    music: '',
                                                    title: '',
                                                    isFacebook: facebook,
                                                    isInstagram: instagram,
                                                  ));
                                            },
                                            onDoneHighlight: (media, title) {
                                              Logger.lOG(title);
                                              context.read<HomeFeedBloc>().add(UploadStoryApiEvent(
                                                    uploadFiles: File(media),
                                                    music: '',
                                                    title: title,
                                                    isFacebook: false,
                                                    isInstagram: false,
                                                  ));
                                            },
                                          ),
                                          withNavBar: false,
                                          customPageRoute: PageRouteBuilder(
                                            opaque: false,
                                            barrierColor: Colors.transparent,
                                            transitionDuration: Duration(milliseconds: 250),
                                            reverseTransitionDuration: Duration(milliseconds: 250),
                                            pageBuilder: (context, animation, secondaryAnimation) {
                                              return flowkarStoryEditor(
                                                centerText: "Start Your Design",
                                                isInstagram: isInstagram,
                                                isfacebook: isFacebook,
                                                onDone: (media, instagram, facebook) {
                                                  context.read<HomeFeedBloc>().add(UploadStoryApiEvent(
                                                      uploadFiles: File(media),
                                                      music: '',
                                                      title: '',
                                                      isFacebook: facebook,
                                                      isInstagram: instagram));
                                                },
                                                onDoneHighlight: (media, title) {
                                                  Logger.lOG(title);
                                                  context.read<HomeFeedBloc>().add(UploadStoryApiEvent(
                                                      uploadFiles: File(media),
                                                      music: '',
                                                      title: title,
                                                      isFacebook: false,
                                                      isInstagram: false));
                                                },
                                              );
                                            },
                                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                              final curvedAnimation =
                                                  CurvedAnimation(parent: animation, curve: Curves.easeInOut);

                                              return SlideTransition(
                                                position: Tween<Offset>(begin: Offset(-1, 0), end: Offset.zero)
                                                    .animate(curvedAnimation),
                                                child: SlideTransition(
                                                  position: Tween<Offset>(begin: Offset.zero, end: Offset(-1, 0))
                                                      .animate(CurvedAnimation(
                                                          parent: secondaryAnimation, curve: Curves.easeInOut)),
                                                  child: child,
                                                ),
                                              );
                                            },
                                          ),
                                        );
                                      },
                                child: CustomImageView(
                                    height: 20.0.h, imagePath: Assets.images.svg.homeFeed.svgAddStory.path)))
                        : const SizedBox.shrink(),
                    isSelect
                        ? Positioned(
                            bottom: -26,
                            child: GestureDetector(
                              onTap: () {},
                              child: Text(
                                title,
                                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 12.sp),
                              ),
                            ),
                          )
                        : SizedBox.shrink()
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> showLocalNotification(Map<String, dynamic> response) async {
    final title = response['title'] ?? 'No Title';
    final subtitle = response['message'] ?? 'No Message';

    const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
      'your_channel_id',
      'your_channel_name',
      channelDescription: 'Your channel description',
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'ticker',
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(android: androidPlatformChannelSpecifics);

    VibrationHelper.singleShortBuzz();
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      subtitle,
      platformChannelSpecifics,
    );
  }

  void showCommentsBottomSheet(
    BuildContext context,
    int postId,
  ) {
    showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      isScrollControlled: true,
      builder: (context) => CommentsBottomSheet(
        postId: postId,
      ),
    );
  }

// MARK: Feed Tab
  Widget _buildFeedTab(HomeFeedState state, SurveyState surveyState) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          if (!connectivityState.isConnected && state.homeFeedLoading && _isRefreshing) {
            return HomeFeedShimmer(showStoryShimmer: true);
          } else if (state.homeFeedLoading) {
            return HomeFeedShimmer(showStoryShimmer: true);
          } else if (!connectivityState.isConnected && state.posts.isEmpty) {
            return HomeFeedShimmer(showStoryShimmer: true);
          } else if (state.posts.isEmpty) {
            if (!connectivityState.isConnected && (state.homeFeedLoading || _isRefreshing)) {
              return ListView(
                physics: AlwaysScrollableScrollPhysics(),
                children: [
                  buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                  ExceptionWidget(
                    imagePath: Assets.images.svg.exception.svgNodatafound.path,
                    showButton: false,
                    title: Lang.of(context).lbl_no_data_found,
                    subtitle: Lang.of(context).lbl_no_post,
                  ),
                ],
              );
            } else {
              return HomeFeedShimmer(showStoryShimmer: true);
            }
          }

          return Stack(
            children: [
              NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  bool currentlyAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;

                  if (scrollInfo is ScrollEndNotification) {
                    final nextPage = state.postResponsemodel?.nextPage;
                    final isLoading = state.isLoadingMore;
                    final hasMoreData = nextPage != null;
                    final isPaginationComplete = !hasMoreData && !isLoading;
                    final isShimmerLoading = state.homeFeedLoading;

                    if (currentlyAtBottom && hasMoreData && !isLoading) {
                      context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: state.postPage + 1));
                    } else if (currentlyAtBottom && isPaginationComplete && !isShimmerLoading) {
                      if (!_wasAtBottomBefore || !_hasShownAllCaughtUp) {
                        setState(() {
                          _showAllCaughtUp = true;
                          _hasShownAllCaughtUp = true;
                        });

                        _allCaughtUpTimer?.cancel();
                        _allCaughtUpTimer = Timer(const Duration(seconds: 3), () {
                          if (mounted) {
                            setState(() {
                              _showAllCaughtUp = false;
                            });
                          }
                        });
                      }
                    }

                    _wasAtBottomBefore = currentlyAtBottom;
                    _isAtBottom = currentlyAtBottom;

                    return true;
                  }

                  if (scrollInfo is ScrollUpdateNotification) {
                    bool newAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;
                    if (_isAtBottom != newAtBottom) {
                      _isAtBottom = newAtBottom;
                      if (!newAtBottom) {
                        _wasAtBottomBefore = false;
                      }
                    }
                  }

                  return false;
                },
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: BouncingScrollPhysics(),
                  slivers: [
                    SliverToBoxAdapter(
                      child: Column(
                        children: [
                          AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              child: state.homeFeedLoading || state.homeFeedVideoLoading || state.storyisloding
                                  ? buildShimmerHorizontalList()
                                  : _buildHorizontalList(state, ctx: context)),
                          buildSizedBoxH(8.0),
                        ],
                      ),
                    ),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final post = state.posts[index];
                          final isLastPost = index == state.posts.length - 1;
                          final shouldInvalidateCache = _postCache.containsKey(post.id) &&
                              (_postCache[post.id]?.key != ValueKey('post_${post.id}') ||
                                  // Add other conditions that would require cache update
                                  _postCache[post.id]?.isLiked != post.isLiked ||
                                  _postCache[post.id]?.isSaved != post.isSaved ||
                                  _postCache[post.id]?.title != post.title ||
                                  _postCache[post.id]?.description != post.description);

                          // Check cache first (without padding)
                          Widget? cachedWidget;
                          if (_postCache.containsKey(post.id) && !shouldInvalidateCache) {
                            cachedWidget = _postCache[post.id];
                          } else {
                            // Create and cache the core widget without padding
                            cachedWidget = RepaintBoundary(
                              child: PostWidget(
                                key: ValueKey('post_${post.id}'),
                                width: post.width,
                                height: post.height,
                                userByIDpost: false,
                                userByIDvideo: false,
                                userVideo: false,
                                userpost: false,
                                isPost: true,
                                isTextPost: post.isTextPost,
                                state: state,
                                index: index,
                                userId: post.user.userId,
                                latestcomments: post.latestComment.toString(),
                                postId: post.id,
                                profileImage: post.user.profileImage.toString(),
                                name: post.user.name,
                                username: post.user.username,
                                postMedia: post.files,
                                thumbnailImage: [],
                                title: post.title,
                                caption:
                                    "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
                                likes: post.likes.toString(),
                                comments: post.commentsCount.toString(),
                                postTime: post.scheduledAt.isNotEmpty ? post.scheduledAt : post.createdAt,
                                isLiked: post.isLiked,
                                isSaved: post.isSaved,
                                taggedIn: post.taggedIn,
                                doubleTap: () {
                                  if (post.isLiked == false && connectivityState.isConnected) {
                                    context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                                  }
                                },
                                likeonTap: () {
                                  context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                                },
                                commentonTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    useRootNavigator: true,
                                    isScrollControlled: true,
                                    enableDrag: true,
                                    backgroundColor: Colors.transparent,
                                    builder: (context) => CommentsBottomSheet(postId: post.id),
                                  );
                                },
                                shareonTap: () {},
                                saveonTap: () {
                                  context.read<HomeFeedBloc>().add(SavedPostSocketEvent(postId: post.id.toString()));
                                },
                              ),
                            );
                            _postCache[post.id] = cachedWidget;
                          }

                          // Always apply padding dynamically based on current position
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: isLastPost ? 80.0 : 0.0,
                            ),
                            child: cachedWidget,
                          );
                        },
                        childCount: state.posts.length,
                      ),
                    ),
                  ],
                ),
              ),
              AllCaughtUpMassage(showAllCaughtUp: _showAllCaughtUp && !state.homeFeedLoading),
              Padding(
                padding: EdgeInsets.only(bottom: 60.h),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Visibility(
                    visible: state.isLoadingMore && !state.homeFeedLoading && connectivityState.isConnected,
                    child: SizedBox(
                      height: 50.h,
                      child: Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

// MARK: Video Tab
  // ignore: unused_element
  Widget _buildVideoTab(HomeFeedState state, SurveyState surveyState) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          if (!connectivityState.isConnected && state.video.isEmpty) {
            return HomeFeedShimmer(showStoryShimmer: true);
          } else if (state.homeFeedVideoLoading) {
            return HomeFeedShimmer(showStoryShimmer: true);
          } else if (state.video.isEmpty) {
            return ListView(
              physics: AlwaysScrollableScrollPhysics(),
              children: [
                buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                ExceptionWidget(
                  imagePath: Assets.images.svg.exception.svgNodatafound.path,
                  showButton: false,
                  title: Lang.of(context).lbl_no_data_found,
                  subtitle: Lang.of(context).lbl_no_post,
                ),
              ],
            );
          }

          return Stack(
            children: [
              NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  bool currentlyAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;

                  if (scrollInfo is ScrollEndNotification) {
                    final nextPage = state.videoResponseModel?.next;
                    final isLoading = state.isVideoLoadingMore;
                    final hasMoreData = nextPage != null;
                    final isPaginationComplete = !hasMoreData && !isLoading;
                    final isShimmerLoading = state.homeFeedVideoLoading;

                    if (currentlyAtBottom && hasMoreData && !isLoading) {
                      context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: state.videoPage + 1));
                    } else if (currentlyAtBottom && isPaginationComplete && !isShimmerLoading) {
                      if (!_wasAtBottomBefore || !_hasShownAllCaughtUp) {
                        setState(() {
                          _showAllCaughtUp = true;
                          _hasShownAllCaughtUp = true;
                        });

                        _allCaughtUpTimer?.cancel();
                        _allCaughtUpTimer = Timer(const Duration(seconds: 3), () {
                          if (mounted) {
                            setState(() {
                              _showAllCaughtUp = false;
                            });
                          }
                        });
                      }
                    }

                    _wasAtBottomBefore = currentlyAtBottom;
                    _isAtBottom = currentlyAtBottom;

                    return true;
                  }

                  if (scrollInfo is ScrollUpdateNotification) {
                    bool newAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;
                    if (_isAtBottom != newAtBottom) {
                      _isAtBottom = newAtBottom;
                      if (!newAtBottom) {
                        _wasAtBottomBefore = false;
                      }
                    }
                  }

                  return false;
                },
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: AlwaysScrollableScrollPhysics(),
                  slivers: [
                    SliverToBoxAdapter(
                      child: Column(
                        children: [
                          buildSizedBoxH(8.0),
                          // if (surveyState.userHomeDataModel?.isbetatester != true)
                          //   FutureBuilder<bool>(
                          //     future: shouldShowBetaPost(),
                          //     builder: (context, snapshot) {
                          //       if (!snapshot.hasData || !snapshot.data!) return SizedBox.shrink();
                          //       return _buildBetaPost();
                          //     },
                          //   ),
                        ],
                      ),
                    ),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final video = state.video[index];
                          final isLastVideo = index == state.video.length - 1;

                          // Check cache without padding consideration
                          if (_videoCache.containsKey(video.id)) {
                            final cachedWidget = _videoCache[video.id]!;

                            // Apply padding dynamically based on current position
                            if (isLastVideo) {
                              return Padding(
                                padding: EdgeInsets.only(bottom: 80.0),
                                child: cachedWidget,
                              );
                            }
                            return cachedWidget;
                          }

                          // Create widget without bottom padding for cache
                          final coreWidget = RepaintBoundary(
                            child: PostWidget(
                              key: ValueKey('video_${video.id}'),
                              width: video.width,
                              height: video.height,
                              isVideo: true,
                              userByIDpost: false,
                              userByIDvideo: false,
                              userVideo: false,
                              userpost: false,
                              state: state,
                              index: index,
                              userId: video.user.userId,
                              latestcomments: video.latestComment.toString(),
                              postId: video.id,
                              profileImage: video.user.profileImage.toString(),
                              thumbnailImage: video.thumbnailFiles.isEmpty ? [] : video.thumbnailFiles,
                              name: video.user.name,
                              username: video.user.username,
                              postMedia: video.files,
                              taggedIn: video.taggedIn,
                              title: video.title,
                              caption:
                                  "${video.title == "''" || video.title.isEmpty ? '' : video.title}${video.description.isEmpty ? '' : video.title == "''" || video.title.isEmpty ? video.description : "\n${video.description}"}",
                              likes: video.likes.toString(),
                              comments: video.commentsCount.toString(),
                              postTime: video.createdAt,
                              isLiked: video.isLiked,
                              isSaved: video.isSaved,
                              doubleTap: () {
                                if (video.isLiked == false) {
                                  context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: video.id));
                                }
                              },
                              likeonTap: () {
                                context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: video.id));
                              },
                              commentonTap: () {
                                showModalBottomSheet(
                                  context: context,
                                  useRootNavigator: true,
                                  isScrollControlled: true,
                                  builder: (context) => CommentsBottomSheet(postId: video.id),
                                );
                              },
                              shareonTap: () {},
                              saveonTap: () {
                                context.read<HomeFeedBloc>().add(SavedPostSocketEvent(postId: video.id.toString()));
                              },
                            ),
                          );

                          // Cache the core widget
                          _videoCache[video.id] = coreWidget;

                          // Return with conditional padding
                          if (isLastVideo) {
                            return Padding(
                              padding: EdgeInsets.only(bottom: 80.0),
                              child: coreWidget,
                            );
                          }
                          return coreWidget;
                        },
                        childCount: state.video.length,
                      ),
                    ),
                  ],
                ),
              ),
              AllCaughtUpMassage(showAllCaughtUp: _showAllCaughtUp && !state.homeFeedVideoLoading),
              Padding(
                padding: EdgeInsets.only(bottom: Platform.isAndroid ? 50.h : 20.h),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Visibility(
                    visible: state.isVideoLoadingMore && !state.homeFeedVideoLoading && connectivityState.isConnected,
                    child: SizedBox(
                      height: 50.h,
                      child: Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLiveUser(HomeFeedState state) {
    final loggedInUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);
    LiveNewStory? loggedInUserlive;

    final liveuser = <LiveNewStory>[];
    final userIdsSeen = <int>{};
    for (var story in state.livenewStoryData) {
      if (story.userId == int.parse(loggedInUserId)) {
        loggedInUserlive ??= story;
      } else if (!userIdsSeen.contains(story.userId)) {
        userIdsSeen.add(story.userId ?? 0);
        liveuser.add(story);
      }
    }
    liveuser.removeWhere((story) => story.userId == int.parse(loggedInUserId));

    final livestories = liveuser.where((story) => story.userId != int.parse(loggedInUserId)).toList();

    return state.liveUserisloding == true
        ? SizedBox.shrink()
        : Padding(
            padding: EdgeInsets.only(left: 0.0.w, top: 0.h),
            child: SizedBox(
              height: 140.h,
              child: Row(
                children: [
                  livestories.isNotEmpty
                      ? ListView.builder(
                          shrinkWrap: true,
                          physics: const ClampingScrollPhysics(parent: BouncingScrollPhysics()),
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.zero,
                          controller: storyController,
                          itemCount: livestories.length,
                          itemBuilder: (context, index) {
                            final story = livestories[index];
                            return InkWell(
                                onTap: () {
                                  // NavigatorService.pushNamed(AppRoutes.livestreamScreen, arguments: [
                                  //   'liv-${livestories[index].userId}',
                                  //   livestories[index].userId.toString(),
                                  //   livestories[index].username.toString(),
                                  //   livestories[index].userprofile.toString(),
                                  // ]);
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: LiveStreamPage(
                                      args: [
                                        'liv-${livestories[index].userId}',
                                        livestories[index].userId.toString(),
                                        livestories[index].username.toString(),
                                        livestories[index].userprofile.toString(),
                                      ],
                                    ),
                                    withNavBar: false,
                                    customPageRoute: PageRouteBuilder(
                                      opaque: false,
                                      barrierColor: Colors.transparent,
                                      transitionDuration: Duration(milliseconds: 250),
                                      reverseTransitionDuration: Duration(milliseconds: 250),
                                      pageBuilder: (context, animation, secondaryAnimation) {
                                        return LiveStreamPage(
                                          args: [
                                            'liv-${livestories[index].userId}',
                                            livestories[index].userId.toString(),
                                            livestories[index].username.toString(),
                                            livestories[index].userprofile.toString(),
                                          ],
                                        );
                                      },
                                      transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                        final curvedAnimation = CurvedAnimation(
                                          parent: animation,
                                          curve: Curves.easeInOut,
                                        );

                                        return SlideTransition(
                                          position: Tween<Offset>(
                                            begin: Offset(0, 1),
                                            end: Offset.zero,
                                          ).animate(curvedAnimation),
                                          child: SlideTransition(
                                            position: Tween<Offset>(
                                              begin: Offset.zero,
                                              end: Offset(0, 1),
                                            ).animate(CurvedAnimation(
                                              parent: secondaryAnimation,
                                              curve: Curves.easeInOut,
                                            )),
                                            child: child,
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                                child: _buildLiveUserItem(
                                  "${story.username}",
                                  story.userprofile?.isEmpty ?? true
                                      ? AssetConstants.pngUserReomve
                                      : "${APIConfig.mainbaseURL}${livestories[index].userprofile}",
                                  isSelect: true,
                                  url: story.stories != null && story.stories!.isNotEmpty
                                      ? story.stories![0].storyfile
                                      : null,
                                ));
                          },
                        )
                      : SizedBox.shrink(),
                ],
              ),
            ),
          );
  }

  Widget _buildLiveUserItem(String title, String imageUrl, {bool isSelect = false, String? url}) {
    return Container(
      height: 120.h,
      width: 85.w,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(10.r),
        // image: DecorationImage(image: NetworkImage(url.toString()), fit: BoxFit.fill),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 5.0.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                    border: Border.all(color: Theme.of(context).textTheme.titleSmall!.color!, width: 1.w),
                  ),
                  child: ClipRRect(
                    clipBehavior: Clip.antiAlias,
                    child: Container(
                      height: 36.h,
                      width: 36.w,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(50.r),
                      ),
                      child: CustomImageView(
                        imagePath: imageUrl,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                buildSizedBoxH(4.0),
                GestureDetector(
                    onTap: () {},
                    child: Container(
                        decoration: BoxDecoration(
                            color: ThemeData().customColors.primaryColor, borderRadius: BorderRadius.circular(8)),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 14.0.h, vertical: 4.0.w),
                          child: Text(
                            "LIVE",
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontSize: 12.0.sp,
                                color: ThemeData().customColors.white,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2.sp),
                          ),
                        ))),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4.0.w, vertical: 3.0.h),
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 12.sp,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
