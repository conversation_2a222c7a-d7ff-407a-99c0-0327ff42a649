// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/story_screen_controller.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/widgets/story_view.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:get/get.dart';

class StoryScreen extends StatelessWidget {
  final List<NewStory> users;
  final int index;
  final bool isHilight;

  const StoryScreen({super.key, required this.users, required this.index, required this.isHilight});

  @override
  Widget build(BuildContext context) {
    StoryScreenController controller = StoryScreenController(users, index, PageController(initialPage: index));
    return Material(
      // color: Colors.black,
      color: Colors.transparent,
      child: GetBuilder<StoryScreenController>(
          init: controller,
          builder: (controllerContext) {
            return PageView(
              allowImplicitScrolling: false,
              controller: controller.pageController,
              onPageChanged: controller.onPageChange,
              children: List.generate(controller.stories.length, (index) {
                final currentUser = users[index];
                return Dismissible(
                  key: ValueKey(currentUser.userId ?? index),
                  movementDuration: Duration(milliseconds: 200),
                  behavior: HitTestBehavior.opaque,
                  dismissThresholds: const {
                    DismissDirection.down: 0.2,
                  },
                  onUpdate: (details) {
                    (details.progress == 0.0) ? controller.storyController.play() : controller.storyController.pause();
                  },
                  resizeDuration: Duration(milliseconds: 200),
                  direction: DismissDirection.down,
                  onDismissed: (direction) {
                    //controller.storyController.pause();
                    NavigatorService.goBack();
                    //Navigator.maybePop(context);
                  },
                  child: StoryView(
                    storyItems: controller.stories[index],
                    inline: true,
                    onStoryShow: controller.onStoryShow,
                    onBack: controller.onPreviousUser,
                    onComplete: controller.onNext,
                    progressPosition: ProgressPosition.top,
                    repeat: false,
                    isHilight: isHilight,
                    controller: controller.storyController,
                    // onVerticalSwipeComplete: (direction) {
                    //   if (direction == Direction.down) {
                    //     Logger.lOG("Vertical $direction");
                    //     NavigatorService.goBack();
                    //   }
                    // },
                    overlayWidget: (item) {
                      return Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 30.h, left: 15.w, right: 15.w),
                            child: Row(
                              children: [
                                // if (currentUser.userprofile?.isNotEmpty ?? false)
                                GestureDetector(
                                  onTap: isHilight
                                      ? null
                                      : () {
                                          PersistentNavBarNavigator.pushNewScreen(
                                            context,
                                            screen: GetUserProfileById(
                                              userId: currentUser.userId,
                                              stackonScreen: true,
                                            ),
                                          );
                                        },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor.withOpacity(0.6),
                                      shape: BoxShape.circle,
                                    ),
                                    padding: const EdgeInsets.all(1),
                                    child: ClipOval(
                                        child: CustomImageView(
                                      height: 40.h,
                                      width: 40.w,
                                      imagePath: currentUser.userprofile?.isEmpty ?? true
                                          ? Assets.images.pngs.other.pngPlaceholder.path
                                          : currentUser.userprofile ?? "",
                                    )),
                                  ),
                                ),
                                buildSizedBoxW(10.0),
                                GestureDetector(
                                  onTap: isHilight
                                      ? null
                                      : () {
                                          // PersistentNavBarNavigator.pushNewScreen(
                                          //   context,
                                          //   screen: GetUserProfileById(
                                          //     userId: currentUser.userId,
                                          //     stackonScreen: true,
                                          //   ),
                                          // );
                                          final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID).toString();
                                          if (currentUser.userId.toString() == currentUserId) {
                                            PersistentNavBarNavigator.pushNewScreen(context,
                                                screen: UserProfileScreen(), withNavBar: false);

                                            // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                                          } else {
                                            PersistentNavBarNavigator.pushNewScreen(context,
                                                screen: GetUserProfileById(
                                                    userId: currentUser.userId, stackonScreen: false),
                                                withNavBar: false);
                                          }
                                        },
                                  child: Text(
                                    currentUser.username ?? "",
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ),
                                Spacer(),
                                if (currentUser.userId?.toString() ==
                                    Prefobj.preferences?.get(Prefkeys.USER_ID).toString())
                                  if (Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false)
                                    isHilight
                                        ? BlocBuilder<UserProfileBloc, UserProfileState>(
                                            builder: (context, state) {
                                              return IconButton(
                                                  onPressed: () {
                                                    controller.storyController.pause();
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible:
                                                          false, // Changed to false to prevent manual close
                                                      builder: (ctx) => BlocListener<UserProfileBloc, UserProfileState>(
                                                        listener: (context, state) {
                                                          // જ્યારે loading false થાય અને dialog ખુલ્લું હોય
                                                          if (!state.isloding) {
                                                            // Dialog close કરો
                                                            Navigator.of(context).pop();
                                                            if (state.highlightStoryData.isNotEmpty) {
                                                              state.highlightStoryData.clear();
                                                            }
                                                            context
                                                                .read<UserProfileBloc>()
                                                                .add(GetHighlightStoryApiEvent());
                                                            // Story ને play કરો
                                                            controller.storyController.play();
                                                          }
                                                        },
                                                        child: BlocBuilder<UserProfileBloc, UserProfileState>(
                                                          builder: (context, state) {
                                                            return CustomAlertDialog(
                                                              imagePath:
                                                                  Assets.images.svg.setting.svgDailogDeleteAccount.path,
                                                              title: "Confirm Deletion",
                                                              subtitle:
                                                                  "This will permanently remove the highLight. Do you want to proceed?",
                                                              onConfirmButtonPressed: () {
                                                                controller.storyController.pause();
                                                                var currentItem = controller.stories[
                                                                        controller.pageController.page?.round() ?? 0]
                                                                    .firstWhereOrNull((it) => !it.shown);
                                                                if (currentItem != null) {
                                                                  context.read<UserProfileBloc>().add(
                                                                      DeleteHighLightStoryEvent(
                                                                          storyId: currentItem.id.toInt()));
                                                                }

                                                                Logger.lOG('Delete HighLight Story tapped');
                                                              },
                                                              confirmButtonText: "Yes",
                                                              cancelButtonText: "Cancel",
                                                              isLoading: state.isloding,
                                                              onCancelButtonPressed: () {
                                                                // Cancel button પર story play કરો
                                                                Navigator.of(context).pop();
                                                                controller.storyController.play();
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  icon: CustomImageView(
                                                    height: 20.h,
                                                    width: 20.w,
                                                    imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                                                    color: Colors.white,
                                                  ));
                                            },
                                          )
                                        : BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                            builder: (context, state) {
                                              return IconButton(
                                                  onPressed: () {
                                                    controller.storyController.pause();
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible:
                                                          false, // Changed to false to prevent manual close
                                                      builder: (ctx) => BlocListener<HomeFeedBloc, HomeFeedState>(
                                                        listener: (context, state) {
                                                          // જ્યારે loading false થાય અને dialog ખુલ્લું હોય
                                                          if (!state.isloding) {
                                                            // Dialog close કરો
                                                            Navigator.of(context).pop();
                                                            if (state.newStoryData.isNotEmpty) {
                                                              state.newStoryData.clear();
                                                            }
                                                            context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
                                                            // Story ને play કરો
                                                            controller.storyController.play();
                                                          }
                                                        },
                                                        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                                          builder: (context, state) {
                                                            return CustomAlertDialog(
                                                              imagePath:
                                                                  Assets.images.svg.setting.svgDailogDeleteAccount.path,
                                                              title: "Confirm Deletion",
                                                              subtitle:
                                                                  "This will permanently remove the story. Do you want to proceed?",
                                                              onConfirmButtonPressed: () {
                                                                controller.storyController.pause();
                                                                var currentItem = controller.stories[
                                                                        controller.pageController.page?.round() ?? 0]
                                                                    .firstWhereOrNull((it) => !it.shown);
                                                                if (currentItem != null) {
                                                                  context.read<HomeFeedBloc>().add(DeleteStoryEvent(
                                                                      storyId: currentItem.id.toInt()));
                                                                }
                                                                Logger.lOG('Delete Story tapped');
                                                              },
                                                              confirmButtonText: "Yes",
                                                              cancelButtonText: "Cancel",
                                                              isLoading: state.isloding,
                                                              onCancelButtonPressed: () {
                                                                // Cancel button પર story play કરો
                                                                Navigator.of(context).pop();
                                                                controller.storyController.play();
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  icon: CustomImageView(
                                                    height: 20.h,
                                                    width: 20.w,
                                                    imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                                                    color: Colors.white,
                                                  ));
                                            },
                                          ),
                                if (currentUser.userId?.toString() !=
                                    Prefobj.preferences?.get(Prefkeys.USER_ID).toString())
                                  Spacer(),
                                IconButton(
                                  onPressed: () {
                                    NavigatorService.goBack();
                                    Logger.lOG("User ID: ${currentUser.userId}");
                                    controller.storyController.pause();
                                  },
                                  icon: Icon(Icons.close_rounded, color: Colors.white),
                                )
                              ],
                            ),
                          ),
                          const Spacer()
                        ],
                      );
                    },
                  ),
                );
              }),
            );
          }),
    );
  }
}

// onNextTap: controller.onChangeOfStory,
// onStoryShowComplete: controller.onUserChange
