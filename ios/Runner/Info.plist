<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>flowkar</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>6.1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.flowkar.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>flowkar</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>FlutterDeepLinkingEnabled</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>Flowkar needs access to your Apple Music library to provide a personalized music experience.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Flowkar requires Bluetooth access to connect with nearby devices for a seamless experience.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Flowkar uses Bluetooth to connect and communicate with nearby devices.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Flowkar needs access to your calendar to help you manage and schedule events seamlessly.</string>
	<key>NSCameraUsageDescription</key>
	<string>Flowkar app need access to your camera to allow you to upload photos, videos and scan QR code.</string>
	<key>NSContactsUsageDescription</key>
	<string>Flowkar requires access to your contacts to connect you with friends and sync your network.</string>
	<key>NSHumanReadableCopyright</key>
	<string></string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Flowkar needs access to your location to provide location-based services, such as navigation and event recommendations.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Flowkar app needs access to location when open.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Flowkar app need access to your microphone to record audio.</string>
	<key>NSMotionUsageDescription</key>
	<string>Flowkar uses motion data to enhance activity tracking and improve your overall experience.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Flowkar app requires access to the photo library to save edited videos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Flowkar app requires access to the photo library to upadate your profile picture.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Flowkar requires speech recognition access to enable voice commands and enhance accessibility.</string>
	<key>NSUserNotificationUsageDescription</key>
	<string>Flowkar app needs access to send you notifications about updates, messages, likes, comments, and other important activities on your posts and account.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UNNotificationPresentationOptionAlert</key>
	<true/>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:api.flowkar.com</string>
	</array>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>ic_upload</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Upload Content</string>
			<key>UIApplicationShortcutItemType</key>
			<string>Upload Content</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>ic_chat</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Messages</string>
			<key>UIApplicationShortcutItemType</key>
			<string>Messages</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>ic_activity</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Recent Activities</string>
			<key>UIApplicationShortcutItemType</key>
			<string>Recent Activities</string>
		</dict>

		<dict>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Remove App</string>
			<key>UIApplicationShortcutItemType</key>
			<string>Remove App</string>
			<key>UIApplicationShortcutItemIconType</key>
			<string>UIApplicationShortcutIconTypeProhibit</string>
		</dict>
	</array>
</dict>
</plist>
